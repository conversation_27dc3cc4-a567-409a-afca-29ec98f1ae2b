import axios from 'axios';
const token = localStorage.getItem('token');
// import { reqUtil } from 'mtex-rams-core';
// const { decodeResult, tryEncodeParam } = reqUtil;

// const encryption = !true;

// post 请求
const aiConfigPost = function ({ url, params = {}, headers = {}, gateWay = '', cancelToken = null }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        const config = {
            headers: {
                token: token
            },
            timeout: 300000 // 5分钟超时
        };

        // 如果提供了cancelToken，添加到配置中
        if (cancelToken) {
            config.cancelToken = cancelToken;
        }

        axios
            .post(gateWay + url, newParams, config)
            .then((res) => {
                let newRes = res.data;
                resolve(newRes);
                return;
            })
            .catch((err) => {
                // 检查是否是取消请求
                if (axios.isCancel(err)) {
                    reject({
                        success: 999,
                        errorMessage: '请求已取消',
                        isCancelled: true
                    });
                } else {
                    reject({
                        success: 111,
                        errorMessage: err.message
                    });
                }
            });
    });
};
// get 请求
export const aiConfigGet = function ({ url, params = {}, headers = {}, gateWay = '' }) {
    let newParams = params;
    if (Object.keys(headers).length > 0) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        axios
            .get(gateWay + url, {
                params: newParams,
                headers: {
                    token: token
                },
                timeout: 300000 // 5分钟超时
            })
            .then((res) => {
                let newRes = res.data;
                resolve(newRes);
            })
            .catch((err) => {
                reject('');
            });
    });
};
export default aiConfigPost;
