<template>
    <div class="chat-page">
        <div class="chat-title-section">
            <div
                v-if="!isEditingTitle"
                class="chat-title"
                @click="startEditTitle"
                :title="'点击编辑标题'"
            >
                {{ displayTitle }}
                <i class="el-icon-edit-outline edit-icon"></i>
            </div>
            <div v-else class="chat-title-editor">
                <el-input
                    ref="titleInput"
                    v-model="editingTitleValue"
                    size="small"
                    :maxlength="50"
                    @blur="saveTitle"
                    @keyup.enter.native="saveTitle"
                    @keyup.esc.native="cancelEditTitle"
                    placeholder="输入对话标题"
                />
            </div>
        </div>
        <div class="chat-container">
            <!-- 聊天消息区域 -->
            <div class="chat-messages custom-scrollbar" ref="messagesContainer">
                <div
                    v-for="(message, index) in messages"
                    :key="index"
                    class="message-item"
                    :class="{
                        'user-message': message.type === 'user',
                        'assistant-message': message.type === 'assistant',
                        'error-message': message.isError
                    }"
                >
                    <div class="message-content">
                        <!-- 用户消息：纯文本显示 -->
                        <div v-if="message.type === 'user'" class="message-text">
                            {{ message.text }}
                        </div>
                        <!-- AI助手消息：Markdown -->
                        <MarkdownRenderer
                            v-if="message.type === 'assistant' && message.text"
                            :content="message.text"
                            :message-index="index"
                            class="message-text"
                        />
                        <!-- AI助手消息为空时的处理 -->
                        <div v-else-if="message.type === 'assistant'" class="message-text">
                            ......
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div v-if="isAiResponding" class="message-item assistant-message">
                    <div class="message-content loading-message">
                        <div class="typing-text">正在调用知识问答服务，请稍候…</div>
                        <div class="typing-dots">
                            <span class="dot"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                        <div class="stop-generation-btn" @click="handleStopGeneration">
                            停止生成
                        </div>
                    </div>
                </div>

                <!-- 空状态提示 -->
                <div v-if="messages.length === 0 && !isAiResponding" class="empty-state">
                    <div class="empty-icon">
                        <img src="~@/img/main/logo-big.png" alt="logo" />
                    </div>
                    <p class="empty-text">开始您的对话吧</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-section">
                <MessageInput
                    :placeholder="placeholder"
                    :model-options="modelOptions"
                    :defaultModel="defaultModel"
                    :selectedModel="selectedModel"
                    :show-mic-button="true"
                    :show-model-details="true"
                    :disabled="isAiResponding"
                    popper-class="dataUsageAssistant-theme model-select-popper"
                    @send-message="handleSendMessage"
                    @mic-click="handleMicClick"
                    @model-change="handleModelChange"
                />
                <!-- 回到底部 -->
                <div
                    class="back-bottom"
                    :class="{ 'is-show': showBackToBottom }"
                    @click="scrollToBottom"
                >
                    <i class="el-icon-arrow-down"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import MessageInput from './MessageInput.vue';
import MarkdownRenderer from './MarkdownRenderer.vue';

export default {
    name: 'ChatPage',
    components: {
        MessageInput,
        MarkdownRenderer
    },
    props: {
        // 聊天标题
        title: {
            type: String,
            default: '新对话'
        },
        // 当前会话ID
        sessionId: {
            type: String,
            default: null
        },
        // 聊天消息列表
        messages: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        selectedModel: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入您的问题...'
        },
        // AI是否正在回复
        isAiResponding: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 控制回到底部按钮显示状态
            showBackToBottom: false,
            // 滚动监听器的节流定时器
            scrollThrottleTimer: null,
            // 标题编辑相关状态
            isEditingTitle: false,
            editingTitleValue: ''
        };
    },
    computed: {
        // 显示的标题
        displayTitle() {
            return this.title || '新对话';
        }
    },
    methods: {
        // 处理发送消息
        handleSendMessage(message) {
            // 添加用户类型标识
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.$emit('send-message', userMessage);
        },

        // 处理停止生成
        handleStopGeneration() {
            this.$emit('stop-generation');
        },

        // 处理麦克风点击
        handleMicClick() {
            this.$emit('mic-click');
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    // 使用平滑滚动动画
                    container.scrollTo({
                        top: container.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            });
        },

        // 处理滚动事件，控制回到底部按钮显示
        handleScroll() {
            // 使用节流优化性能
            if (this.scrollThrottleTimer) {
                clearTimeout(this.scrollThrottleTimer);
            }

            this.scrollThrottleTimer = setTimeout(() => {
                const container = this.$refs.messagesContainer;
                if (!container) return;

                const scrollTop = container.scrollTop;
                const scrollHeight = container.scrollHeight;
                const clientHeight = container.clientHeight;

                // 计算距离底部的距离
                const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

                // 显示条件：向下滚动超过400px且距离底部超过150px
                const shouldShow = distanceFromBottom > 150;

                this.showBackToBottom = shouldShow;
            }, 100); // 100ms节流
        },

        // 处理模型变更
        handleModelChange(newModel) {
            this.$emit('model-change', newModel);
        },

        // 开始编辑标题
        startEditTitle() {
            if (!this.sessionId) return; // 只有在有会话ID时才允许编辑

            this.isEditingTitle = true;
            this.editingTitleValue = this.displayTitle;

            this.$nextTick(() => {
                if (this.$refs.titleInput) {
                    this.$refs.titleInput.focus();
                    this.$refs.titleInput.select();
                }
            });
        },

        // 保存标题
        saveTitle() {
            const newTitle = this.editingTitleValue.trim();
            if (newTitle && newTitle !== this.displayTitle && this.sessionId) {
                this.$emit('update-title', this.sessionId, newTitle);
            }
            this.cancelEditTitle();
        },

        // 取消编辑标题
        cancelEditTitle() {
            this.isEditingTitle = false;
            this.editingTitleValue = '';
        }
    },
    watch: {
        messages: {
            handler() {
                this.scrollToBottom();
            },
            deep: true
        },
        isAiResponding: {
            handler() {
                this.scrollToBottom();
            }
        }
    },
    mounted() {
        // 添加滚动事件监听器
        const container = this.$refs.messagesContainer;
        if (container) {
            container.addEventListener('scroll', this.handleScroll);
        }
    },
    beforeDestroy() {
        // 清理滚动事件监听器和定时器
        const container = this.$refs.messagesContainer;
        if (container) {
            container.removeEventListener('scroll', this.handleScroll);
        }
        if (this.scrollThrottleTimer) {
            clearTimeout(this.scrollThrottleTimer);
        }
    }
};
</script>

<style scoped lang="less">
.chat-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;

    .chat-title-section {
        width: 100%;
        height: 3rem;
        background: rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 1rem;

        .chat-title {
            font-weight: 500;
            font-size: 16px;
            color: #222222;
            line-height: 22px;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            max-width: 100%;

            &:hover {
                background: rgba(0, 0, 0, 0.05);

                .edit-icon {
                    opacity: 1;
                }
            }

            .edit-icon {
                opacity: 0;
                transition: opacity 0.2s ease;
                font-size: 14px;
                color: #666;
            }
        }

        .chat-title-editor {
            max-width: 20rem;
            width: 100%;

            /deep/ .el-input__inner {
                text-align: center;
                font-weight: 500;
                font-size: 16px;
                border: 1px solid #0265fe;
                border-radius: 0.25rem;

                &:focus {
                    border-color: #0265fe;
                    box-shadow: 0 0 0 2px rgba(2, 101, 254, 0.2);
                }
            }
        }
    }

    .chat-container {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin: 0 auto;
        width: 100%;

        .chat-messages {
            --bar-width: 0;
            --bar-height: 0;
            width: 60rem;
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .message-item {
                display: flex;

                &.user-message {
                    justify-content: flex-end;

                    .message-content {
                        background: #cde1ff;
                        border-radius: 0.75rem 0.25rem 0.75rem 0.75rem;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.assistant-message {
                    justify-content: flex-start;

                    .message-content {
                        background: transparent;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.error-message {
                    justify-content: flex-start;

                    .message-content {
                        background: #fef2f2;
                        border: 1px solid #fecaca;
                        color: #dc2626;
                        max-width: 70%;
                    }
                }

                .message-content {
                    padding: 0.75rem 1rem;
                    border-radius: 0.75rem;

                    .message-text {
                        font-size: 0.875rem;
                        line-height: 1.6;
                        color: #333;
                    }

                    .message-time {
                        margin-top: 0.25rem;
                        font-size: 0.75rem;
                        opacity: 0.7;
                    }

                    // AI回复加载状态样式
                    &.loading-message {
                        display: flex;
                        flex-direction: column;
                        gap: 1rem;

                        .typing-text {
                            font-weight: 400;
                            font-size: 1rem;
                            color: #222222;
                            line-height: 1.5rem;
                        }

                        .typing-dots {
                            display: flex;
                            gap: 0.5rem;

                            .dot {
                                width: 0.5rem;
                                height: 0.5rem;
                                background: #666;
                                border-radius: 50%;
                                animation: typing 1.4s infinite ease-in-out;

                                &:nth-child(1) {
                                    animation-delay: 0s;
                                }
                                &:nth-child(2) {
                                    animation-delay: 0.2s;
                                }
                                &:nth-child(3) {
                                    animation-delay: 0.4s;
                                }
                            }
                        }

                        .stop-generation-btn {
                            width: fit-content;
                            margin-top: 0.5rem;
                            font-family: HONORSansCN, HONORSansCN;
                            font-weight: 400;
                            font-size: 1rem;
                            color: #0265fe;
                            line-height: 1.5rem;
                            cursor: pointer;
                            transition: color 0.2s;

                            &:hover {
                                color: lighten(#0265fe, 20%);
                            }
                        }
                    }
                }
            }

            .empty-state {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .empty-icon {
                    width: 4rem;
                    height: 4rem;
                    margin-bottom: 1rem;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .empty-text {
                    color: #999;
                    font-size: 0.875rem;
                }
            }
        }

        .chat-input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;
            position: relative;
            .back-bottom {
                width: 2rem;
                height: 2rem;
                position: absolute;
                top: -1.25rem;
                right: 0.625rem;
                transform: translateY(-100%);
                cursor: pointer;
                border-radius: 50%;
                background-color: #fff;
                box-shadow: inset 0 0 0 0.0625rem darken(#fff, 5%);
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease-in-out;
                // 默认隐藏状态
                opacity: 0;
                visibility: hidden;
                transform: translateY(-100%) scale(0.8);

                // 显示状态
                &.is-show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(-100%) scale(1);
                }

                &:hover {
                    background-color: darken(#fff, 5%);
                    transform: translateY(-100%) scale(1.05);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                }

                .el-icon-arrow-down {
                    font-size: 1rem;
                    font-weight: 600;
                    transition: transform 0.2s ease;
                }

                &:active {
                    transform: translateY(-100%) scale(0.95);
                }
            }
        }
    }
}

// 打字动画关键帧
@keyframes typing {
    0%,
    60%,
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}
</style>
<!-- Markdown相关样式已迁移到MarkdownRenderer组件 -->
