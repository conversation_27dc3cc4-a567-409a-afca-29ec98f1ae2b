<template>
    <div class="sidebar" :style="{ width: isCollapse ? '3.75rem' : '18.75rem' }">
        <!-- 展开状态 -->
        <div
            class="sidebar-open"
            :style="{
                width: isCollapse ? '0' : '100%',
                height: isCollapse ? '0' : '100%',
                opacity: isCollapse ? 0 : 1
            }"
        >
            <div class="sidebar-header">
                <div class="logo-section">
                    <img src="~@/img/main/logo.png" alt="logo" class="logo" />
                    <img src="~@/img/main/main-title.png" alt="logo-title" class="title" />
                    <div class="collapse-btn" @click="handleCollapse(true)"></div>
                </div>
                <div class="new-chat" @click="handleNewChat">
                    <div class="new-chat-icon"></div>
                    <span class="new-chat-text">新对话</span>
                </div>
            </div>
            <div class="search-section">
                <el-autocomplete
                    v-model="searchText"
                    placeholder="搜索"
                    prefix-icon="el-icon-search"
                    clearable
                    class="search-input"
                    popper-class="search-autocomplete-popper"
                    :fetch-suggestions="querySearchAsync"
                    :trigger-on-focus="true"
                    @input="handleSearch"
                    @select="handleSearchSelect"
                >
                    <template slot-scope="{ item }">
                        <div class="search-suggestion-item">
                            <div class="search-item-text">{{ item.label }}</div>
                        </div>
                    </template>
                </el-autocomplete>
            </div>
            <div class="menu-section custom-scrollbar">
                <template v-for="menuGroup in filteredMenuGroups">
                    <div class="menu-item" :key="`title-${menuGroup.key}`">
                        <span class="menu-title">{{ menuGroup.title }}</span>
                    </div>
                    <div class="menu-group" :key="`group-${menuGroup.key}`">
                        <div
                            class="menu-group-item"
                            :class="{ active: activeMenuItem === item.id }"
                            v-for="item in menuGroup.items"
                            :key="item.id"
                            @click="handleMenuItemClick(item)"
                        >
                            <span class="menu-group-text">{{ item.text }}</span>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        <!-- 收起状态 -->
        <div
            class="sidebar-close"
            :style="{ width: isCollapse ? '100%' : '0', height: isCollapse ? '100%' : '0' }"
        >
            <img src="~@/img/main/logo.png" alt="logo" class="logo" />
            <div class="collapse-btn" @click="handleCollapse(false)"></div>
            <el-tooltip
                class="item"
                effect="dark"
                content="新对话"
                placement="right"
                popper-class="new-chat-popper"
            >
                <div class="new-chat" @click="handleNewChat">
                    <div class="new-chat-icon"></div>
                </div>
            </el-tooltip>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Sidebar',
    props: {
        menuGroups: {
            type: Array,
            default: () => []
        },
        activeMenuItem: {
            type: [String, Number],
            default: null
        }
    },
    data() {
        return {
            isCollapse: false,
            searchText: ''
        };
    },
    computed: {
        // 根据搜索文本过滤菜单项
        filteredMenuGroups() {
            if (!this.searchText.trim()) {
                return this.menuGroups;
            }

            return this.menuGroups
                .map((group) => ({
                    ...group,
                    items: group.items.filter((item) =>
                        item.text.toLowerCase().includes(this.searchText.toLowerCase())
                    )
                }))
                .filter((group) => group.items.length > 0);
        },

        // 获取所有搜索数据源
        allSearchItems() {
            const allItems = [];

            // 合并所有菜单组的数据
            this.menuGroups.forEach((group) => {
                group.items.forEach((item) => {
                    allItems.push({
                        value: item.text,
                        label: item.text,
                        id: item.id,
                        type: item.type,
                        groupKey: group.key,
                        groupTitle: group.title,
                        timestamp: item.timestamp
                    });
                });
            });

            return allItems;
        }
    },
    methods: {
        // 处理侧边栏收起
        handleCollapse(val) {
            this.isCollapse = val;
            this.$emit('collapse-change', val);
        },

        // 处理新对话点击
        handleNewChat() {
            this.$emit('new-chat');
        },

        // 处理搜索输入
        handleSearch(value) {
            this.$emit('search', value);
        },

        // 自动完成查询方法
        querySearchAsync(queryString, callback) {
            if (!queryString || queryString.trim() === '') {
                callback([]);
                return;
            }

            const results = this.allSearchItems.filter((item) => {
                return item.value.toLowerCase().includes(queryString.toLowerCase());
            });

            // 限制结果数量，避免列表过长
            const limitedResults = results.slice(0, 10);

            // 为每个结果添加显示格式
            const formattedResults = limitedResults.map((item) => ({
                ...item,
                value: item.value,
                label: item.value,
                groupInfo: item.groupTitle
            }));

            callback(formattedResults);
        },

        // 处理自动完成选择
        handleSearchSelect(item) {
            // 设置搜索文本为选中项的值
            this.searchText = item.value;

            // 触发菜单项点击事件
            const menuItem = {
                id: item.id,
                text: item.value,
                type: item.type,
                timestamp: item.timestamp
            };

            this.handleMenuItemClick(menuItem);
            this.$emit('search-select', item);
        },

        // 处理菜单项点击
        handleMenuItemClick(item) {
            this.$emit('menu-item-click', item);
        }
    }
};
</script>

<style scoped lang="less">
// 侧边栏样式
.sidebar {
    width: 18.75rem;
    background: rgba(255, 255, 255, 0.8);
    height: 100vh;
    transition: all 0.2s ease-in;

    .sidebar-open {
        width: 100%;
        height: 100%;
        opacity: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: width 0.2s ease-out, all 0.2s ease-out;
        .sidebar-header {
            padding: 1.75rem 0.75rem 0;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            .logo-section {
                display: flex;
                align-items: center;
                gap: 0.625rem;

                .logo {
                    width: 2.25rem;
                    height: 2.25rem;
                }

                .title {
                    width: 10rem;
                    height: 1.5rem;
                }

                .collapse-btn {
                    margin-left: auto;
                    width: 1.5rem;
                    height: 1.5rem;
                    background-image: url('~@/img/main/sidebar-collapse.png');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    cursor: pointer;
                }
            }
            .new-chat {
                height: 2.5rem;
                background: rgba(2, 101, 254, 0.05);
                border-radius: 0.5rem;
                border: 0.0625rem solid rgba(2, 101, 254, 0.5);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0 0.75rem;
                cursor: pointer;
                user-select: none;
                transition: all 0.2 ease-in-out;
                &-icon {
                    width: 1.25rem;
                    height: 1.25rem;
                    background-image: url('~@/img/main/new-chat.png');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                }
                &-text {
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #0265fe;
                    line-height: 1.25rem;
                }
                &:hover {
                    background: rgba(2, 101, 254, 0.1);
                }
            }
        }

        .search-section {
            padding: 1.5rem 0.75rem 1.875rem;

            /deep/.el-autocomplete.search-input {
                width: 100%;

                .el-input__inner {
                    height: 2.25rem;
                    background: #ffffff;
                    border-radius: 0.5rem;
                    border: 0.0625rem solid rgba(213, 214, 216, 0.5);
                }
            }
        }

        .menu-section {
            min-height: 0;
            flex: 1;
            padding: 0 0.75rem 1.5rem;
            overflow-y: auto;
            --thumb-color: rgba(0, 0, 0, 0.1);

            .menu-item {
                padding: 0 0.75rem 0.5rem;
                .menu-title {
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #666666;
                    line-height: 1.25rem;
                }
            }

            .menu-group {
                margin-bottom: 1.875rem;

                .menu-group-item {
                    padding: 0.5rem 0.75rem;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                    border-radius: 0.5rem;
                    &:hover {
                        background: rgb(239 246 255);
                    }
                    &.active {
                        background: rgb(219 234 254);
                        &:hover {
                            background: rgb(219 234 254);
                        }
                    }
                }
                .menu-group-text {
                    font-size: 0.875rem;
                    color: #374151;
                    display: block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
    .sidebar-close {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 1.75rem;
        overflow: hidden;
        transition: width 0.2s ease-in;
        .logo {
            width: 2.25rem;
            height: 2.25rem;
            margin-bottom: 1.25rem;
        }
        .collapse-btn {
            width: 1.5rem;
            height: 1.5rem;
            background-image: url('~@/img/main/sidebar-collapse.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;
            margin-bottom: 0.875rem;
        }
        .new-chat {
            width: 2.25rem;
            height: 2.25rem;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            &:hover {
                background: rgba(2, 101, 254, 0.1);
            }
            &-icon {
                width: 1.25rem;
                height: 1.25rem;
                background-image: url('~@/img/main/new-chat-gray.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }
    }
}
</style>

<style lang="less">
.search-autocomplete-popper {
    max-height: 18.75rem;
    overflow-y: auto;
    background: #ffffff;
    box-shadow: 0 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0 0.375rem 1rem 0 rgba(0, 0, 0, 0.08), 0 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.5rem;
}
</style>
